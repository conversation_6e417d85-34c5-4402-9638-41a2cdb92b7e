import { MaterialIcons } from '@expo/vector-icons';
import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { FlatList, StyleSheet, TouchableOpacity, View } from 'react-native';
import { Surface, Text, TextInput } from 'react-native-paper';

interface Place {
  id: number;
  name: string;
}

interface DestinationSidebarProps {
  places: Place[];
  selectedPlaceId?: number;
  onSelectPlace: (placeId: number, placeName: string) => void;
  title?: string;
}

const DestinationSidebar: React.FC<DestinationSidebarProps> = ({
  places,
  selectedPlaceId,
  onSelectPlace,
  title = 'Chọn điểm đến'
}) => {
  const { t } = useTranslation();
  const [searchText, setSearchText] = useState('');

  const filteredPlaces = places.filter(place =>
    place.name.toLowerCase().includes(searchText.toLowerCase())
  );

  const renderPlaceItem = ({ item }: { item: Place }) => {
    const isSelected = selectedPlaceId === item.id;
    
    return (
      <TouchableOpacity
        onPress={() => onSelectPlace(item.id, item.name)}
        style={styles.placeItemContainer}
      >
        <Surface 
          style={[
            styles.placeItem,
            isSelected && styles.selectedPlaceItem
          ]} 
          elevation={isSelected ? 3 : 1}
        >
          <View style={styles.placeContent}>
            <MaterialIcons 
              name="location-on" 
              size={20} 
              color={isSelected ? '#2D5BFF' : '#666'} 
            />
            <Text style={[
              styles.placeName,
              isSelected && styles.selectedPlaceName
            ]}>
              {item.name}
            </Text>
          </View>
          {isSelected && (
            <MaterialIcons name="check-circle" size={20} color="#2D5BFF" />
          )}
        </Surface>
      </TouchableOpacity>
    );
  };

  return (
    <Surface style={styles.container} elevation={2}>
      <View style={styles.header}>
        <Text style={styles.title}>{title}</Text>
      </View>
      
      <View style={styles.searchContainer}>
        <TextInput
          placeholder={t('tripSelection.searchPlaceholder', 'Tìm điểm đến...')}
          value={searchText}
          onChangeText={setSearchText}
          style={styles.searchInput}
          left={<TextInput.Icon icon={() => <MaterialIcons name="search" size={20} color="#666" />} />}
          mode="outlined"
          dense
        />
      </View>

      <FlatList
        data={filteredPlaces}
        renderItem={renderPlaceItem}
        keyExtractor={(item) => item.id.toString()}
        style={styles.placesList}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={styles.placesListContent}
      />
    </Surface>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
    borderRadius: 12,
    margin: 8,
  },
  header: {
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
  },
  searchContainer: {
    padding: 16,
    paddingBottom: 8,
  },
  searchInput: {
    backgroundColor: '#f8f9fa',
  },
  placesList: {
    flex: 1,
  },
  placesListContent: {
    padding: 8,
  },
  placeItemContainer: {
    marginBottom: 8,
  },
  placeItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 12,
    borderRadius: 8,
    backgroundColor: '#fff',
    borderWidth: 1,
    borderColor: '#f0f0f0',
  },
  selectedPlaceItem: {
    borderColor: '#2D5BFF',
    backgroundColor: '#f8f9ff',
  },
  placeContent: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  placeName: {
    marginLeft: 8,
    fontSize: 14,
    color: '#333',
    flex: 1,
  },
  selectedPlaceName: {
    color: '#2D5BFF',
    fontWeight: '500',
  },
});

export default DestinationSidebar;
