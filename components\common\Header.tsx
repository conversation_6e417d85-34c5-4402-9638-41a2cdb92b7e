import { Ionicons } from '@expo/vector-icons';
import { router } from 'expo-router';
import React from 'react';
import { StatusBar, StyleSheet, TouchableOpacity, View } from 'react-native';
import { Surface, Text } from 'react-native-paper';

interface HeaderProps {
  title?: string;
  subtitle?: string;
  showBackButton?: boolean;
  rightComponent?: React.ReactNode;
  onBackPress?: () => void;
  backgroundColor?: string;
}

const Header: React.FC<HeaderProps> = ({
  title,
  subtitle,
  showBackButton = true,
  rightComponent,
  onBackPress,
  backgroundColor = '#2D5BFF'
}) => {
  const handleBackPress = () => {
    if (onBackPress) {
      onBackPress();
    } else {
      router.back();
    }
  };

  return (
    <>
      <StatusBar barStyle="light-content" backgroundColor={backgroundColor} />
      <Surface style={[styles.headerSurface, { backgroundColor }]} elevation={4}>
        <View style={styles.header}>
          {showBackButton && (
            <TouchableOpacity onPress={handleBackPress} style={styles.backButton}>
              <Ionicons name="arrow-back" size={28} color="#fff" />
            </TouchableOpacity>
          )}
          
          <View style={[styles.headerTitleContainer, !showBackButton && { marginLeft: 0 }]}>
            {title && <Text style={styles.headerTitle}>{title}</Text>}
            {subtitle && (
              <View style={styles.headerSubtitleContainer}>
                <Text style={styles.headerSubtitle}>{subtitle}</Text>
              </View>
            )}
          </View>
          
          {rightComponent && (
            <View style={styles.rightComponentContainer}>
              {rightComponent}
            </View>
          )}
        </View>
      </Surface>
    </>
  );
};

const styles = StyleSheet.create({
  headerSurface: {
    backgroundColor: 'rgba(45, 91, 255, 0.8)',
    borderBottomLeftRadius: 20,
    borderBottomRightRadius: 20,
    marginBottom: 16,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 20,
    paddingHorizontal: 24,
  },
  backButton: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  headerTitleContainer: {
    flex: 1,
    marginLeft: 16,
    alignItems: 'center',
  },
  headerTitle: {
    color: '#fff',
    fontSize: 24,
    fontWeight: 'bold',
    textAlign: 'center',
  },
  headerSubtitleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 4,
  },
  headerSubtitle: {
    color: 'rgba(255, 255, 255, 0.8)',
    fontSize: 16,
    marginLeft: 4,
  },
  rightComponentContainer: {
    marginLeft: 16,
  },
});

export default Header;
