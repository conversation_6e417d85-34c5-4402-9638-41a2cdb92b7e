import React from 'react';
import { useTranslation } from 'react-i18next';
import { ActivityIndicator, FlatList, StyleSheet, View } from 'react-native';
import { Surface, Text } from 'react-native-paper';
import EmptyState from './EmptyState';

interface TripsListProps {
  trips: any[];
  loading: boolean;
  fromPlaceId?: number;
  toPlaceId?: number;
  renderTripItem: ({ item }: { item: any }) => React.ReactElement;
  contentContainerStyle?: any;
  showsVerticalScrollIndicator?: boolean;
}

const TripsList: React.FC<TripsListProps> = ({
  trips,
  loading,
  fromPlaceId,
  toPlaceId,
  renderTripItem,
  contentContainerStyle,
  showsVerticalScrollIndicator = false
}) => {
  const { t } = useTranslation();

  if (loading) {
    return (
      <Surface style={styles.container} elevation={2}>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#2D5BFF" />
          <Text style={styles.loadingText}>{t('tripScreen.loading')}</Text>
        </View>
      </Surface>
    );
  }

  if (!fromPlaceId || !toPlaceId) {
    return (
      <Surface style={styles.container} elevation={2}>
        <View style={styles.header}>
          <Text style={styles.title}>Chọn chuyến</Text>
        </View>
        <View style={styles.emptyStateContainer}>
          <EmptyState
            title={t('emptyState.selectToTitle')}
            subtitle={t('emptyState.selectToSubtitle')}
          />
        </View>
      </Surface>
    );
  }

  if (trips.length === 0) {
    return (
      <Surface style={styles.container} elevation={2}>
        <View style={styles.header}>
          <Text style={styles.title}>Chọn chuyến</Text>
        </View>
        <View style={styles.emptyStateContainer}>
          <EmptyState
            title={t('emptyState.notFoundTitle')}
            subtitle={t('emptyState.notFoundSubtitle')}
          />
        </View>
      </Surface>
    );
  }

  return (
    <Surface style={styles.container} elevation={2}>
      <View style={styles.header}>
        <Text style={styles.title}>Chọn chuyến</Text>
        <Text style={styles.subtitle}>{trips.length} chuyến khả dụng</Text>
      </View>
      <FlatList
        data={trips}
        renderItem={renderTripItem}
        keyExtractor={item => item.id}
        contentContainerStyle={[styles.listContent, contentContainerStyle]}
        showsVerticalScrollIndicator={showsVerticalScrollIndicator}
        style={styles.list}
      />
    </Surface>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
    borderRadius: 12,
    margin: 8,
  },
  header: {
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
  },
  subtitle: {
    fontSize: 14,
    color: '#666',
    marginTop: 4,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 40,
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: '#666',
  },
  emptyStateContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 40,
  },
  list: {
    flex: 1,
  },
  listContent: {
    padding: 8,
  },
});

export default TripsList;
